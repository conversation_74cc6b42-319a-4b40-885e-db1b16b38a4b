package top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronLeft
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.KeyboardDoubleArrowLeft
import androidx.compose.material.icons.filled.KeyboardDoubleArrowRight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.tv.material3.Text
import top.yogiczy.mytv.tv.ui.material.ProgressBar
import top.yogiczy.mytv.tv.ui.theme.MyTvTheme
import java.text.SimpleDateFormat
import java.util.Locale
import kotlin.math.max
import kotlin.math.min

@Composable
fun VideoPlayerControllerPositionCtrl(
    modifier: Modifier = Modifier,
    currentPositionProvider: () -> Long = { 0L },
    durationProvider: () -> Pair<Long, Long> = { 0L to 0L },
    seekToPositionProvider: () -> Long? = { null },
    seekForward: (Long) -> Unit = {},
    seekNext: (Long) -> Unit = {},
) {

    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        VideoPlayerControllerBtn(
            imageVector = Icons.Default.KeyboardDoubleArrowLeft,
            onSelect = { seekForward(1000L * 60) },
        )
        VideoPlayerControllerBtn(
            imageVector = Icons.Default.ChevronLeft,
            onSelect = { seekForward(1000L * 10) },
        )

        VideoPlayerControllerBtn(
            imageVector = Icons.Default.ChevronRight,
            onSelect = { seekNext(1000L * 10) },
        )
        VideoPlayerControllerBtn(
            imageVector = Icons.Default.KeyboardDoubleArrowRight,
            onSelect = { seekNext(1000L * 60) },
        )

        VideoPlayerControllerPositionProgress(
            modifier = Modifier.padding(start = 10.dp),
            currentPositionProvider = { seekToPositionProvider() ?: currentPositionProvider() },
            durationProvider = durationProvider,
        )
    }
}

@Composable
private fun VideoPlayerControllerPositionProgress(
    modifier: Modifier = Modifier,
    currentPositionProvider: () -> Long = { 0L },
    durationProvider: () -> Pair<Long, Long> = { 0L to 0L },
) {
    val currentPosition = currentPositionProvider()
    val duration = durationProvider()
    val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())

    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = "00:00:00",
        )

        ProgressBar(
            process = (currentPosition - duration.first) / (duration.second - duration.first).toFloat(),
            modifier = Modifier
                .weight(1f)
                .height(6.dp),
        )

        Text(
            text = "${timeFormat.format(currentPosition)} / ${timeFormat.format(duration.second)}",
        )
    }
}