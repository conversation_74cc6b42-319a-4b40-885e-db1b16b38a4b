<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.github.mytv.android"
    android:versionCode="1"
    android:versionName="2.0.0.0" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="36" />

    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />

    <queries>
        <package android:name="com.google.android.webview" />
    </queries>

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- X5权限需求 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- X5权限需求 -->
    <uses-permission android:name="android.permission.GET_TASKS" /> <!-- X5权限需求 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- X5权限需求 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- X5权限需求 -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <permission
        android:name="com.github.mytv.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.github.mytv.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="top.yogiczy.mytv.tv.MyTVApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:banner="@drawable/ic_banner"
        android:debuggable="true"
        android:directBootAware="false"
        android:extractNativeLibs="true"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:localeConfig="@xml/_generated_res_locale_config"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/Theme.MyTV"
        android:usesCleartextTraffic="true" >
        <property
            android:name="android.window.PROPERTY_COMPAT_ALLOW_RESTRICTED_RESIZABILITY"
            android:value="true" />

        <activity
            android:name="top.yogiczy.mytv.tv.MainActivity"
            android:configChanges="screenSize|smallestScreenSize|screenLayout|orientation|screenLayout|keyboardHidden"
            android:exported="true"
            android:resizeableActivity="true"
            android:screenOrientation="sensorLandscape"
            android:supportsPictureInPicture="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name="top.yogiczy.mytv.tv.CrashHandlerActivity" />

        <receiver
            android:name="top.yogiczy.mytv.tv.BootReceiver"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.RECEIVE_BOOT_COMPLETED" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <service android:name="top.yogiczy.mytv.tv.HttpServerService" />
        <service
            android:name="top.yogiczy.mytv.tv.X5CorePreLoadService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="androidx.appcompat.app.AppLocalesMetadataHolderService"
            android:enabled="false"
            android:exported="false" >
            <meta-data
                android:name="autoStoreLocales"
                android:value="true" />
        </service>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.github.mytv.android.FileProvider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- <meta-data -->
        <!-- android:name="io.sentry.auto-init" -->
        <!-- android:value="false" /> -->

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.github.mytv.android.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="okhttp3.internal.platform.PlatformInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <activity
            android:name="androidx.compose.ui.tooling.PreviewActivity"
            android:exported="true" />
        <activity
            android:name="androidx.activity.ComponentActivity"
            android:exported="true"
            android:theme="@android:style/Theme.Material.Light.NoActionBar" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>