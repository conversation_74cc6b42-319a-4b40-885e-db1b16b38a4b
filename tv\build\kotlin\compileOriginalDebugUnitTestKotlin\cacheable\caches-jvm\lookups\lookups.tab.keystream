  currentTimeMillis java.lang.System  	compareTo kotlin.Long  minus kotlin.Long  plus kotlin.Long  EpgProgramme 	org.junit  System 	org.junit  Test 	org.junit  assertFalse 	org.junit  
assertTrue 	org.junit  assertFalse org.junit.Assert  
assertTrue org.junit.Assert  EpgProgramme 'top.yogiczy.mytv.core.data.entities.epg  endAt 4top.yogiczy.mytv.core.data.entities.epg.EpgProgramme  startAt 4top.yogiczy.mytv.core.data.entities.epg.EpgProgramme  EpgProgramme 0top.yogiczy.mytv.tv.ui.screensold.epg.components  EpgProgrammeItemTest 0top.yogiczy.mytv.tv.ui.screensold.epg.components  System 0top.yogiczy.mytv.tv.ui.screensold.epg.components  Test 0top.yogiczy.mytv.tv.ui.screensold.epg.components  assertFalse 0top.yogiczy.mytv.tv.ui.screensold.epg.components  
assertTrue 0top.yogiczy.mytv.tv.ui.screensold.epg.components  EpgProgramme Etop.yogiczy.mytv.tv.ui.screensold.epg.components.EpgProgrammeItemTest  System Etop.yogiczy.mytv.tv.ui.screensold.epg.components.EpgProgrammeItemTest  assertFalse Etop.yogiczy.mytv.tv.ui.screensold.epg.components.EpgProgrammeItemTest  
assertTrue Etop.yogiczy.mytv.tv.ui.screensold.epg.components.EpgProgrammeItemTest  Calendar 	java.util  DAY_OF_MONTH java.util.Calendar  HOUR_OF_DAY java.util.Calendar  MILLISECOND java.util.Calendar  MINUTE java.util.Calendar  MONTH java.util.Calendar  SECOND java.util.Calendar  YEAR java.util.Calendar  get java.util.Calendar  getInstance java.util.Calendar  set java.util.Calendar  timeInMillis java.util.Calendar  Pair kotlin  to kotlin  times 
kotlin.Int  div kotlin.Long  to kotlin.Long  toFloat kotlin.Long  first kotlin.Pair  second kotlin.Pair  maxOf kotlin.collections  minOf kotlin.collections  maxOf kotlin.comparisons  minOf kotlin.comparisons  maxOf kotlin.sequences  minOf kotlin.sequences  maxOf kotlin.text  minOf kotlin.text  Calendar 	org.junit  assertEquals 	org.junit  maxOf 	org.junit  minOf 	org.junit  to 	org.junit  assertEquals org.junit.Assert  Calendar 7top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller  System 7top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller  Test 7top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller  VideoPlayerControllerScreenTest 7top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller  assertEquals 7top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller  
assertTrue 7top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller  maxOf 7top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller  minOf 7top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller  to 7top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller  Calendar Wtop.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.VideoPlayerControllerScreenTest  System Wtop.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.VideoPlayerControllerScreenTest  assertEquals Wtop.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.VideoPlayerControllerScreenTest  
assertTrue Wtop.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.VideoPlayerControllerScreenTest  maxOf Wtop.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.VideoPlayerControllerScreenTest  minOf Wtop.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.VideoPlayerControllerScreenTest  to Wtop.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.VideoPlayerControllerScreenTest                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                