-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:89:9-97:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:93:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:91:13-64
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:92:13-37
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:90:13-62
manifest
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:2:1-105:12
INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:2:1-105:12
INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:2:1-105:12
INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:2:1-105:12
MERGED from [lib-decoder-ffmpeg-release.aar] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\004bd0d8c0ad8158f8101d4b846be5cf\transformed\lib-decoder-ffmpeg-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\03c59f01b4d50573fe2a148684529e0c\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [:core:util] C:\Users\<USER>\StudioProjects\mytv-Internal\core\util\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:core:data] C:\Users\<USER>\StudioProjects\mytv-Internal\core\data\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:core:designsystem] C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:webview] C:\Users\<USER>\StudioProjects\mytv-Internal\webview\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-svg:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c6a436083dac8d6e1f797e8d85a2d33a\transformed\coil-svg-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\32eff55312555ce2d8190f03e99f993c\transformed\coil-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fc4261d33b0a14914280604e0536b6e\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6adab9f4229c3b3ae8789dd2d066aea5\transformed\coil-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e50953d4c7a9ac9dc958badd2300824c\transformed\coil-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\020e5d6b34aebf27949453c72c15711f\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3912e4719bfc557199d9fe6db52fdadc\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\71973fdd2f8b1122e619daae98b107aa\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ddd749e30b286065f16123ee26b3561c\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [org.videolan.android:libvlc-all:3.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c56babab14465a2b9141ca5bd8847d6c\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e233c8c34feef57770352f89d6dc437f\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.tv:tv-material:1.1.0-alpha01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2e15333b06288624cfdf2bb8145264a5\transformed\tv-material-1.1.0-alpha01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\35f391f48f1163378f0ecf1dfb563ddd\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5a0df1d88ffeb2fe5c28eef9532e5ea1\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\44c809ae125ace27321eb9d460eae097\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3d7a69eb87bc8f8fbf9824702ded5186\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e4a290dba5f52ccc9bc02699d6e15cbe\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02640f0c169d8b00ec49a074cf41671a\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\378dfbdbc5873f6857065242f2ab1670\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\feb492531419f91732c5ed62fdf38203\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7a8fe9f1a4070004f54aeadff7b4e6c0\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cdd23b539dbc00d913771dd5e581033e\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d75b8b2779e4f472e9737bfc301f4453\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7a6478912ec95fc8623b5d56a0747dfb\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\75512f4a55b17bb529701cf2bb99a89c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e6be84dac09f1cf71b60ffb0a323744a\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d72833986ff72743426c7a8fe6d28294\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\16388f0ff652dcde21ee67561e42b45e\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-ui:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\07b9e5f639314a52052890932176ce08\transformed\media3-ui-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource-rtmp:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\73655796ca4b5b6c2a95fc308e195705\transformed\media3-datasource-rtmp-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c4e01fddcd47432bcd53398d51f91044\transformed\media3-extractor-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\91fd340c836a21b0e4bf6e670b99bdf6\transformed\media3-container-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b6fdd6e304df85ac1336eb961764246a\transformed\media3-datasource-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f5ce98f0b0087592b4ff77f962ae16b4\transformed\media3-decoder-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fa8c3999468a3506d751ba95c8c0a5d7\transformed\media3-database-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\358f1e075975623cc5903d930942a0c2\transformed\media3-common-1.8.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\93080bfed0ab62b60e50779af3e9c516\transformed\media3-exoplayer-hls-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e300361de118c79d4bf99d57a30bf21a\transformed\media3-exoplayer-rtsp-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\afdc267ed872decf36bb057fd881d2b5\transformed\media3-exoplayer-dash-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f9b06ca478dc0d80bbb10bdba09fbbf2\transformed\media3-exoplayer-smoothstreaming-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\75b82da373a07aa04f7fa0a8c467d672\transformed\media3-exoplayer-1.8.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7ae59608a6d047f5c6f2f97f0c2aea87\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d088f371e4e55b43a2fd72ff3aaf316e\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fc03dcc3f615e4df7f55b2f7cd27230e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0e12b3fa53cfb1c15d8319bce3d377e5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\05e9d0d2e10b8eba81042b396dc0a717\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\55620df8e20644c0db547653d4240a6c\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6cc0108aeab12ae380c7aaa9d8573179\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\570e3369e22915cc262b1e7e973fd640\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f0a478be89953a95074297b170916831\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9d2e411833feae80665103b61d1f81a5\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2c874c40304132e2ca8aeccd4eb2e013\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0c2dacb64d2efe16ee1e4880b16631d2\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2bacf44565379dfdcb6dc8feb78d2977\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1adc9163ec439131765cba2c5e5bb9bc\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5905c7a4927ab724d0b8b9db5e395b74\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d706509388ba9b83cf41a34ab4ab5ac3\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ed3df65abdf36ada0f3b31b25f3257d7\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97f5d082273a31c2e98d32d1cfb9b977\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\17d19a2679fb5d5179e83ecedef042c1\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\530f990be78988a89b54fb399d2f13ca\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8fbc2c64d4d2e37aba96d609faba9b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6a9f56086a03986ac2ba6bfc8e06c3fc\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\815d967b835de3d48b8afef15ebdc0fc\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1f33ade3c27328b89cd5125a6583afca\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e676d94fe60cf383bbd8021b7bd3b8fd\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ef0a3cbae419d3d6231bb478f353b5f\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\51c8e2087ee7a6b4f4f2aab35d9300f3\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\590d5d9f0c1b298e5ae551dab5db24f1\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8f4fd84af88f03c9ffe1183146d5eac2\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\59444e5b41bb370dd2f68c8a2485bbe3\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\916ebe35a8b4483cca2454bb1271ac3a\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\eecc1d2cff1361704a44141235ec0fc1\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8f2dce2376513ee7c0da29cf1a5a2ca2\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\efd4f45183943e5cd4b0e92f459d9985\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ab2d23b418b2c32928e316e8039dbf9\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.alexzhirkevich:qrose-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b42f04748110809fad7b13a9ee724f0d\transformed\qrose-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.alexzhirkevich:qrose-core-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1a7840e3efa9727e9096b83147084eab\transformed\qrose-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c137ef5392da19ca09c541eab409f058\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2b0a39031ec5b673c46b494682ca4edc\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\aeb9a86c1b81e519b10e73282b97b3ee\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0c82a76523f617aef246a3faf4397c6a\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\eb144a7e7ea3621f88ffa2729c228327\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8921169f99cd0954331bc3e5cf81cf91\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b88b462695746df3cef716fad132b6b\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\82d848edac1325890757a73a2b663f2f\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c898278013e5378db9f9f257d0ca5744\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8ae42d858055e18da776cb0101cdafe7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dc17c1b704eb56b60d68ffa66582c540\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\AndroidManifest.xml:2:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dc2ecfa64d0fc23fa74b9241c6f9e7b5\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cb8ff67d61ece4bdff01aa2ec7802156\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5091df0f9e6efb5834737ff61c30ccb8\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5dc22e6218e1130e058a581b9de8f0e3\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1025927014659fdb065f26c19765705a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d6bc23ee506929b82a048d647e782b7d\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\049074c486bb67d32c32450542cab8b6\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b95fa3561316cca74e978c4b2815a702\transformed\startup-runtime-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ab1c5cefe6062fc3ea21fcadfae5fda\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ba03dfa7c6b63bfc80041415ab17c876\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5517848656ffffbf7538b78c80ef4e72\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3298508a7b8558dfadf54eb2e03d36bb\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97bb220814ff49c25eeadede1853ef32\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ce3ff8a849f3999a5b45e96c1b3ab3e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\72a408fd3658204627ff4e7f92ff5d38\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3de960adb74a5a841183758921b7506f\transformed\androidasync-3.1.0\AndroidManifest.xml:2:1-16:12
MERGED from [wang.harlon.quickjs:wrapper-android:3.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\bd404f1a89b4bb36407c692e019915c8\transformed\wrapper-android-3.2.3\AndroidManifest.xml:2:1-7:12
MERGED from [:ijkplayer-java] C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [io.antmedia:rtmp-client:3.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a1f0ae9f76206ffe87a94ea5bef4357f\transformed\rtmp-client-3.2.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ae5ae3e62b79e92816de5f5ce2beea2\transformed\androidsvg-aar-1.4\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:2:11-69
uses-feature#android.hardware.touchscreen
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:5:5-7:36
	android:required
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:7:9-33
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:6:9-52
uses-feature#android.software.leanback
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:8:5-10:36
	android:required
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:10:9-33
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:9:9-49
queries
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:12:5-14:15
package#com.google.android.webview
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:13:9-62
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:13:18-59
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:16:5-67
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\AndroidManifest.xml:8:5-67
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\AndroidManifest.xml:8:5-67
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3de960adb74a5a841183758921b7506f\transformed\androidasync-3.1.0\AndroidManifest.xml:11:5-67
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3de960adb74a5a841183758921b7506f\transformed\androidasync-3.1.0\AndroidManifest.xml:11:5-67
MERGED from [io.antmedia:rtmp-client:3.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a1f0ae9f76206ffe87a94ea5bef4357f\transformed\rtmp-client-3.2.0\AndroidManifest.xml:9:5-67
MERGED from [io.antmedia:rtmp-client:3.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a1f0ae9f76206ffe87a94ea5bef4357f\transformed\rtmp-client-3.2.0\AndroidManifest.xml:9:5-67
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:16:22-64
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:17:5-81
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:17:22-78
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:18:5-83
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:18:22-80
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:19:5-80
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:19:22-77
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:20:5-67
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:20:22-65
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:21:5-76
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:21:22-73
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\358f1e075975623cc5903d930942a0c2\transformed\media3-common-1.8.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\358f1e075975623cc5903d930942a0c2\transformed\media3-common-1.8.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\75b82da373a07aa04f7fa0a8c467d672\transformed\media3-exoplayer-1.8.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\75b82da373a07aa04f7fa0a8c467d672\transformed\media3-exoplayer-1.8.0\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:22:22-76
uses-permission#android.permission.GET_TASKS
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:23:22-65
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:24:5-75
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:24:22-72
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:25:5-81
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:25:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:26:5-28:40
	tools:ignore
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:28:9-37
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:27:9-66
application
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:30:5-103:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\03c59f01b4d50573fe2a148684529e0c\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\03c59f01b4d50573fe2a148684529e0c\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3912e4719bfc557199d9fe6db52fdadc\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3912e4719bfc557199d9fe6db52fdadc\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8fbc2c64d4d2e37aba96d609faba9b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8fbc2c64d4d2e37aba96d609faba9b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8921169f99cd0954331bc3e5cf81cf91\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8921169f99cd0954331bc3e5cf81cf91\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b88b462695746df3cef716fad132b6b\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b88b462695746df3cef716fad132b6b\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:22:5-27:19
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\AndroidManifest.xml:10:5-20:19
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\AndroidManifest.xml:10:5-20:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b95fa3561316cca74e978c4b2815a702\transformed\startup-runtime-1.2.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b95fa3561316cca74e978c4b2815a702\transformed\startup-runtime-1.2.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ba03dfa7c6b63bfc80041415ab17c876\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ba03dfa7c6b63bfc80041415ab17c876\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3de960adb74a5a841183758921b7506f\transformed\androidasync-3.1.0\AndroidManifest.xml:13:5-14:19
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3de960adb74a5a841183758921b7506f\transformed\androidasync-3.1.0\AndroidManifest.xml:13:5-14:19
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:37:9-52
	android:extractNativeLibs
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:45:9-41
	android:largeHeap
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:42:9-33
	android:icon
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:34:9-43
	android:banner
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:33:9-45
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:36:9-69
	android:directBootAware
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:43:9-40
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:38:9-35
	android:label
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:35:9-41
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:41:9-43
	tools:targetApi
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:44:9-28
	android:allowBackup
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:32:9-35
	android:theme
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:39:9-42
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:40:9-44
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:31:9-40
property#android.window.PROPERTY_COMPAT_ALLOW_RESTRICTED_RESIZABILITY
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:46:9-118
	android:value
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:46:95-115
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:46:19-94
activity#top.yogiczy.mytv.tv.MainActivity
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:47:9-60:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:52:13-56
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:50:13-36
	android:supportsPictureInPicture
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:53:13-52
	android:resizeableActivity
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:51:13-46
	tools:ignore
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:54:13-42
	android:configChanges
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:49:13-119
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:48:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:55:13-59:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:56:17-69
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:56:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:57:17-77
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:57:27-74
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:58:17-86
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:58:27-83
activity#top.yogiczy.mytv.tv.CrashHandlerActivity
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:62:9-58
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:62:19-55
receiver#top.yogiczy.mytv.tv.BootReceiver
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:63:9-72:20
	android:enabled
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:65:13-35
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:66:13-37
	android:permission
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:67:13-75
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:64:13-41
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+category:name:android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:68:13-71:29
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:69:17-79
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:69:25-76
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:70:17-76
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:70:27-73
service#top.yogiczy.mytv.tv.HttpServerService
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:74:9-54
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:74:18-51
service#top.yogiczy.mytv.tv.X5CorePreLoadService
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:75:9-79:40
	android:enabled
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:78:13-35
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:79:13-37
	android:permission
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:77:13-69
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:76:13-49
service#androidx.appcompat.app.AppLocalesMetadataHolderService
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:80:9-87:19
	android:enabled
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:82:13-36
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:83:13-37
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:81:13-82
meta-data#autoStoreLocales
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:84:13-86:36
	android:value
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:86:13-33
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:85:13-44
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:94:13-96:54
	android:resource
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:96:17-51
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml:95:17-67
uses-sdk
INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
MERGED from [lib-decoder-ffmpeg-release.aar] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\004bd0d8c0ad8158f8101d4b846be5cf\transformed\lib-decoder-ffmpeg-release\AndroidManifest.xml:20:5-44
MERGED from [lib-decoder-ffmpeg-release.aar] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\004bd0d8c0ad8158f8101d4b846be5cf\transformed\lib-decoder-ffmpeg-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\03c59f01b4d50573fe2a148684529e0c\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\03c59f01b4d50573fe2a148684529e0c\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [:core:util] C:\Users\<USER>\StudioProjects\mytv-Internal\core\util\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:util] C:\Users\<USER>\StudioProjects\mytv-Internal\core\util\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:data] C:\Users\<USER>\StudioProjects\mytv-Internal\core\data\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:data] C:\Users\<USER>\StudioProjects\mytv-Internal\core\data\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:designsystem] C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:core:designsystem] C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:webview] C:\Users\<USER>\StudioProjects\mytv-Internal\webview\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:webview] C:\Users\<USER>\StudioProjects\mytv-Internal\webview\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-svg:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c6a436083dac8d6e1f797e8d85a2d33a\transformed\coil-svg-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-svg:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c6a436083dac8d6e1f797e8d85a2d33a\transformed\coil-svg-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\32eff55312555ce2d8190f03e99f993c\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\32eff55312555ce2d8190f03e99f993c\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fc4261d33b0a14914280604e0536b6e\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fc4261d33b0a14914280604e0536b6e\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6adab9f4229c3b3ae8789dd2d066aea5\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6adab9f4229c3b3ae8789dd2d066aea5\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e50953d4c7a9ac9dc958badd2300824c\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e50953d4c7a9ac9dc958badd2300824c\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\020e5d6b34aebf27949453c72c15711f\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\020e5d6b34aebf27949453c72c15711f\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3912e4719bfc557199d9fe6db52fdadc\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3912e4719bfc557199d9fe6db52fdadc\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\71973fdd2f8b1122e619daae98b107aa\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\71973fdd2f8b1122e619daae98b107aa\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ddd749e30b286065f16123ee26b3561c\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ddd749e30b286065f16123ee26b3561c\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [org.videolan.android:libvlc-all:3.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\AndroidManifest.xml:7:5-44
MERGED from [org.videolan.android:libvlc-all:3.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\AndroidManifest.xml:7:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c56babab14465a2b9141ca5bd8847d6c\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c56babab14465a2b9141ca5bd8847d6c\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e233c8c34feef57770352f89d6dc437f\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e233c8c34feef57770352f89d6dc437f\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tv:tv-material:1.1.0-alpha01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2e15333b06288624cfdf2bb8145264a5\transformed\tv-material-1.1.0-alpha01\AndroidManifest.xml:5:5-44
MERGED from [androidx.tv:tv-material:1.1.0-alpha01] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2e15333b06288624cfdf2bb8145264a5\transformed\tv-material-1.1.0-alpha01\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\35f391f48f1163378f0ecf1dfb563ddd\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\35f391f48f1163378f0ecf1dfb563ddd\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5a0df1d88ffeb2fe5c28eef9532e5ea1\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5a0df1d88ffeb2fe5c28eef9532e5ea1\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\44c809ae125ace27321eb9d460eae097\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\44c809ae125ace27321eb9d460eae097\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3d7a69eb87bc8f8fbf9824702ded5186\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3d7a69eb87bc8f8fbf9824702ded5186\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e4a290dba5f52ccc9bc02699d6e15cbe\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e4a290dba5f52ccc9bc02699d6e15cbe\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02640f0c169d8b00ec49a074cf41671a\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\02640f0c169d8b00ec49a074cf41671a\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\378dfbdbc5873f6857065242f2ab1670\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\378dfbdbc5873f6857065242f2ab1670\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\feb492531419f91732c5ed62fdf38203\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\feb492531419f91732c5ed62fdf38203\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7a8fe9f1a4070004f54aeadff7b4e6c0\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7a8fe9f1a4070004f54aeadff7b4e6c0\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cdd23b539dbc00d913771dd5e581033e\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cdd23b539dbc00d913771dd5e581033e\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d75b8b2779e4f472e9737bfc301f4453\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d75b8b2779e4f472e9737bfc301f4453\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7a6478912ec95fc8623b5d56a0747dfb\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7a6478912ec95fc8623b5d56a0747dfb\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\75512f4a55b17bb529701cf2bb99a89c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\75512f4a55b17bb529701cf2bb99a89c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e6be84dac09f1cf71b60ffb0a323744a\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e6be84dac09f1cf71b60ffb0a323744a\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d72833986ff72743426c7a8fe6d28294\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d72833986ff72743426c7a8fe6d28294\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\16388f0ff652dcde21ee67561e42b45e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\16388f0ff652dcde21ee67561e42b45e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\07b9e5f639314a52052890932176ce08\transformed\media3-ui-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\07b9e5f639314a52052890932176ce08\transformed\media3-ui-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-rtmp:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\73655796ca4b5b6c2a95fc308e195705\transformed\media3-datasource-rtmp-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-rtmp:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\73655796ca4b5b6c2a95fc308e195705\transformed\media3-datasource-rtmp-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c4e01fddcd47432bcd53398d51f91044\transformed\media3-extractor-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c4e01fddcd47432bcd53398d51f91044\transformed\media3-extractor-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\91fd340c836a21b0e4bf6e670b99bdf6\transformed\media3-container-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\91fd340c836a21b0e4bf6e670b99bdf6\transformed\media3-container-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b6fdd6e304df85ac1336eb961764246a\transformed\media3-datasource-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b6fdd6e304df85ac1336eb961764246a\transformed\media3-datasource-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f5ce98f0b0087592b4ff77f962ae16b4\transformed\media3-decoder-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f5ce98f0b0087592b4ff77f962ae16b4\transformed\media3-decoder-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fa8c3999468a3506d751ba95c8c0a5d7\transformed\media3-database-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fa8c3999468a3506d751ba95c8c0a5d7\transformed\media3-database-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\358f1e075975623cc5903d930942a0c2\transformed\media3-common-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\358f1e075975623cc5903d930942a0c2\transformed\media3-common-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\93080bfed0ab62b60e50779af3e9c516\transformed\media3-exoplayer-hls-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\93080bfed0ab62b60e50779af3e9c516\transformed\media3-exoplayer-hls-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e300361de118c79d4bf99d57a30bf21a\transformed\media3-exoplayer-rtsp-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e300361de118c79d4bf99d57a30bf21a\transformed\media3-exoplayer-rtsp-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\afdc267ed872decf36bb057fd881d2b5\transformed\media3-exoplayer-dash-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\afdc267ed872decf36bb057fd881d2b5\transformed\media3-exoplayer-dash-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f9b06ca478dc0d80bbb10bdba09fbbf2\transformed\media3-exoplayer-smoothstreaming-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f9b06ca478dc0d80bbb10bdba09fbbf2\transformed\media3-exoplayer-smoothstreaming-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\75b82da373a07aa04f7fa0a8c467d672\transformed\media3-exoplayer-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.8.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\75b82da373a07aa04f7fa0a8c467d672\transformed\media3-exoplayer-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7ae59608a6d047f5c6f2f97f0c2aea87\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\7ae59608a6d047f5c6f2f97f0c2aea87\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d088f371e4e55b43a2fd72ff3aaf316e\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d088f371e4e55b43a2fd72ff3aaf316e\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fc03dcc3f615e4df7f55b2f7cd27230e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\fc03dcc3f615e4df7f55b2f7cd27230e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0e12b3fa53cfb1c15d8319bce3d377e5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0e12b3fa53cfb1c15d8319bce3d377e5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\05e9d0d2e10b8eba81042b396dc0a717\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\05e9d0d2e10b8eba81042b396dc0a717\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\55620df8e20644c0db547653d4240a6c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\55620df8e20644c0db547653d4240a6c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6cc0108aeab12ae380c7aaa9d8573179\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6cc0108aeab12ae380c7aaa9d8573179\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\570e3369e22915cc262b1e7e973fd640\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\570e3369e22915cc262b1e7e973fd640\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f0a478be89953a95074297b170916831\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f0a478be89953a95074297b170916831\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9d2e411833feae80665103b61d1f81a5\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9d2e411833feae80665103b61d1f81a5\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2c874c40304132e2ca8aeccd4eb2e013\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2c874c40304132e2ca8aeccd4eb2e013\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0c2dacb64d2efe16ee1e4880b16631d2\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0c2dacb64d2efe16ee1e4880b16631d2\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2bacf44565379dfdcb6dc8feb78d2977\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2bacf44565379dfdcb6dc8feb78d2977\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1adc9163ec439131765cba2c5e5bb9bc\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1adc9163ec439131765cba2c5e5bb9bc\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5905c7a4927ab724d0b8b9db5e395b74\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5905c7a4927ab724d0b8b9db5e395b74\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d706509388ba9b83cf41a34ab4ab5ac3\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d706509388ba9b83cf41a34ab4ab5ac3\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ed3df65abdf36ada0f3b31b25f3257d7\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ed3df65abdf36ada0f3b31b25f3257d7\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97f5d082273a31c2e98d32d1cfb9b977\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97f5d082273a31c2e98d32d1cfb9b977\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\17d19a2679fb5d5179e83ecedef042c1\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\17d19a2679fb5d5179e83ecedef042c1\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\530f990be78988a89b54fb399d2f13ca\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\530f990be78988a89b54fb399d2f13ca\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8fbc2c64d4d2e37aba96d609faba9b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8fbc2c64d4d2e37aba96d609faba9b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6a9f56086a03986ac2ba6bfc8e06c3fc\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\6a9f56086a03986ac2ba6bfc8e06c3fc\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\815d967b835de3d48b8afef15ebdc0fc\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\815d967b835de3d48b8afef15ebdc0fc\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1f33ade3c27328b89cd5125a6583afca\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1f33ade3c27328b89cd5125a6583afca\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e676d94fe60cf383bbd8021b7bd3b8fd\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\e676d94fe60cf383bbd8021b7bd3b8fd\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ef0a3cbae419d3d6231bb478f353b5f\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ef0a3cbae419d3d6231bb478f353b5f\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\51c8e2087ee7a6b4f4f2aab35d9300f3\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\51c8e2087ee7a6b4f4f2aab35d9300f3\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\590d5d9f0c1b298e5ae551dab5db24f1\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\590d5d9f0c1b298e5ae551dab5db24f1\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8f4fd84af88f03c9ffe1183146d5eac2\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8f4fd84af88f03c9ffe1183146d5eac2\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\59444e5b41bb370dd2f68c8a2485bbe3\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\59444e5b41bb370dd2f68c8a2485bbe3\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\916ebe35a8b4483cca2454bb1271ac3a\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\916ebe35a8b4483cca2454bb1271ac3a\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\eecc1d2cff1361704a44141235ec0fc1\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\eecc1d2cff1361704a44141235ec0fc1\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8f2dce2376513ee7c0da29cf1a5a2ca2\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8f2dce2376513ee7c0da29cf1a5a2ca2\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\efd4f45183943e5cd4b0e92f459d9985\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\efd4f45183943e5cd4b0e92f459d9985\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ab2d23b418b2c32928e316e8039dbf9\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ab2d23b418b2c32928e316e8039dbf9\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.alexzhirkevich:qrose-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b42f04748110809fad7b13a9ee724f0d\transformed\qrose-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.alexzhirkevich:qrose-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b42f04748110809fad7b13a9ee724f0d\transformed\qrose-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.alexzhirkevich:qrose-core-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1a7840e3efa9727e9096b83147084eab\transformed\qrose-core-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.alexzhirkevich:qrose-core-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1a7840e3efa9727e9096b83147084eab\transformed\qrose-core-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c137ef5392da19ca09c541eab409f058\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c137ef5392da19ca09c541eab409f058\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2b0a39031ec5b673c46b494682ca4edc\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2b0a39031ec5b673c46b494682ca4edc\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\aeb9a86c1b81e519b10e73282b97b3ee\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\aeb9a86c1b81e519b10e73282b97b3ee\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0c82a76523f617aef246a3faf4397c6a\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0c82a76523f617aef246a3faf4397c6a\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\eb144a7e7ea3621f88ffa2729c228327\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\eb144a7e7ea3621f88ffa2729c228327\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8921169f99cd0954331bc3e5cf81cf91\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8921169f99cd0954331bc3e5cf81cf91\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b88b462695746df3cef716fad132b6b\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b88b462695746df3cef716fad132b6b\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\82d848edac1325890757a73a2b663f2f\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\82d848edac1325890757a73a2b663f2f\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c898278013e5378db9f9f257d0ca5744\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c898278013e5378db9f9f257d0ca5744\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8ae42d858055e18da776cb0101cdafe7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8ae42d858055e18da776cb0101cdafe7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dc17c1b704eb56b60d68ffa66582c540\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dc17c1b704eb56b60d68ffa66582c540\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\AndroidManifest.xml:6:5-44
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\AndroidManifest.xml:6:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dc2ecfa64d0fc23fa74b9241c6f9e7b5\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dc2ecfa64d0fc23fa74b9241c6f9e7b5\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cb8ff67d61ece4bdff01aa2ec7802156\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\cb8ff67d61ece4bdff01aa2ec7802156\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5091df0f9e6efb5834737ff61c30ccb8\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5091df0f9e6efb5834737ff61c30ccb8\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5dc22e6218e1130e058a581b9de8f0e3\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5dc22e6218e1130e058a581b9de8f0e3\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1025927014659fdb065f26c19765705a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1025927014659fdb065f26c19765705a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d6bc23ee506929b82a048d647e782b7d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d6bc23ee506929b82a048d647e782b7d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\049074c486bb67d32c32450542cab8b6\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\049074c486bb67d32c32450542cab8b6\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b95fa3561316cca74e978c4b2815a702\transformed\startup-runtime-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b95fa3561316cca74e978c4b2815a702\transformed\startup-runtime-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ab1c5cefe6062fc3ea21fcadfae5fda\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4ab1c5cefe6062fc3ea21fcadfae5fda\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ba03dfa7c6b63bfc80041415ab17c876\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ba03dfa7c6b63bfc80041415ab17c876\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5517848656ffffbf7538b78c80ef4e72\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\5517848656ffffbf7538b78c80ef4e72\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3298508a7b8558dfadf54eb2e03d36bb\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3298508a7b8558dfadf54eb2e03d36bb\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97bb220814ff49c25eeadede1853ef32\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\97bb220814ff49c25eeadede1853ef32\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ce3ff8a849f3999a5b45e96c1b3ab3e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ce3ff8a849f3999a5b45e96c1b3ab3e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\72a408fd3658204627ff4e7f92ff5d38\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\72a408fd3658204627ff4e7f92ff5d38\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3de960adb74a5a841183758921b7506f\transformed\androidasync-3.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.koushikdutta.async:androidasync:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\3de960adb74a5a841183758921b7506f\transformed\androidasync-3.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [wang.harlon.quickjs:wrapper-android:3.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\bd404f1a89b4bb36407c692e019915c8\transformed\wrapper-android-3.2.3\AndroidManifest.xml:5:5-44
MERGED from [wang.harlon.quickjs:wrapper-android:3.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\bd404f1a89b4bb36407c692e019915c8\transformed\wrapper-android-3.2.3\AndroidManifest.xml:5:5-44
MERGED from [:ijkplayer-java] C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:ijkplayer-java] C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [io.antmedia:rtmp-client:3.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a1f0ae9f76206ffe87a94ea5bef4357f\transformed\rtmp-client-3.2.0\AndroidManifest.xml:5:5-7:41
MERGED from [io.antmedia:rtmp-client:3.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\a1f0ae9f76206ffe87a94ea5bef4357f\transformed\rtmp-client-3.2.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ae5ae3e62b79e92816de5f5ce2beea2\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.caverock:androidsvg-aar:1.4] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\1ae5ae3e62b79e92816de5f5ce2beea2\transformed\androidsvg-aar-1.4\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8fbc2c64d4d2e37aba96d609faba9b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8fbc2c64d4d2e37aba96d609faba9b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\AndroidManifest.xml:11:9-19:20
MERGED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\AndroidManifest.xml:11:9-19:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b95fa3561316cca74e978c4b2815a702\transformed\startup-runtime-1.2.0\AndroidManifest.xml:24:9-28:34
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b95fa3561316cca74e978c4b2815a702\transformed\startup-runtime-1.2.0\AndroidManifest.xml:24:9-28:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\81b7e7be4ffbe616279de4f7e6cb6abb\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.github.mytv.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.github.mytv.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c64c27f9b5a3bd14c8d70afc2c5cfe89\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8fbc2c64d4d2e37aba96d609faba9b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8fbc2c64d4d2e37aba96d609faba9b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b8fbc2c64d4d2e37aba96d609faba9b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8921169f99cd0954331bc3e5cf81cf91\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8921169f99cd0954331bc3e5cf81cf91\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8921169f99cd0954331bc3e5cf81cf91\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b88b462695746df3cef716fad132b6b\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b88b462695746df3cef716fad132b6b\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b88b462695746df3cef716fad132b6b\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4b88b462695746df3cef716fad132b6b\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:24:13-63
meta-data#okhttp3.internal.platform.PlatformInitializer
ADDED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\AndroidManifest.xml:16:13-18:52
	android:value
		ADDED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\AndroidManifest.xml:18:17-49
	android:name
		ADDED from [com.squareup.okhttp3:okhttp-android:5.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\AndroidManifest.xml:17:17-77
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\00fe24db3366b2d1b1b79377d7181d3d\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
