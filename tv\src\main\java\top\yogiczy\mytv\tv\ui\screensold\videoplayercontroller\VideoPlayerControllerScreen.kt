package top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller

import androidx.annotation.IntRange
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.tv.material3.Text
import top.yogiczy.mytv.tv.ui.material.Drawer
import top.yogiczy.mytv.tv.ui.material.DrawerPosition
import top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.components.VideoPlayerControllerPositionCtrl
import top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.components.VideoPlayerControllerStateCtrl
import top.yogiczy.mytv.tv.ui.theme.MyTvTheme
import top.yogiczy.mytv.tv.ui.tooling.PreviewWithLayoutGrids
import top.yogiczy.mytv.tv.ui.utils.backHandler
import top.yogiczy.mytv.tv.ui.utils.focusOnLaunchedSaveable
import top.yogiczy.mytv.tv.ui.utils.handleKeyEvents
import top.yogiczy.mytv.tv.R
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.consumeAsFlow
import kotlinx.coroutines.flow.debounce
import kotlin.math.max
import kotlin.math.min
import androidx.compose.ui.res.stringResource
import java.util.Calendar

@Composable
fun VideoPlayerControllerScreen(
    modifier: Modifier = Modifier,
    isVideoPlayerPlayingProvider: () -> Boolean = { false },
    isVideoPlayerBufferingProvider: () -> Boolean = { false },
    videoPlayerCurrentPositionProvider: () -> Long = { 0L },
    videoPlayerDurationProvider: () -> Pair<Long, Long> = { 0L to 0L },
    onVideoPlayerPlay: () -> Unit = {},
    onVideoPlayerPause: () -> Unit = {},
    onVideoPlayerSeekTo: (Long) -> Unit = {},
    onClose: () -> Unit = {},
) {
    var seekToPosition by remember { mutableStateOf<Long?>(null) }

    val debounce = rememberDebounce(
        wait = 1000L,
        func = {
            seekToPosition?.let { nnSeekToPosition ->
                val startPosition = videoPlayerDurationProvider().first
                onVideoPlayerSeekTo(nnSeekToPosition - startPosition)
                seekToPosition = null
            }
        },
    )
    LaunchedEffect(seekToPosition) {
        if (seekToPosition != null) debounce.active()
    }

    fun seekForward(ms: Long) {
        val currentPosition = videoPlayerCurrentPositionProvider()
        val startPosition = videoPlayerDurationProvider().first
        seekToPosition = max(startPosition, (seekToPosition ?: currentPosition) - ms)
    }



    fun seekNext(ms: Long) {
        val currentPosition = videoPlayerCurrentPositionProvider()
        val endPosition = videoPlayerDurationProvider().second
        seekToPosition = min(
            if (endPosition <= 0L) Long.MAX_VALUE else min(endPosition, System.currentTimeMillis()),
            (seekToPosition ?: currentPosition) + ms
        )
    }

    Drawer(
        modifier = modifier.backHandler { onClose() }
            .handleKeyEvents(
                onUp = { seekForward(1000L * 10) },
                onLongUp = { seekForward(1000L * 60) },
                onDown = { seekNext(1000L * 10) },
                onLongDown = { seekNext(1000L * 60) }
            ),
        onDismissRequest = onClose,
        position = DrawerPosition.Bottom,
        header = { Text(stringResource(R.string.ui_channel_view_playback_control)) },
    ) {
        Row(
            modifier = Modifier.padding(top = 10.dp),
            horizontalArrangement = Arrangement.spacedBy(20.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            VideoPlayerControllerStateCtrl(
                modifier = Modifier.focusOnLaunchedSaveable(),
                isPlayingProvider = isVideoPlayerPlayingProvider,
                isBufferingProvider = isVideoPlayerBufferingProvider,
                onPlay = onVideoPlayerPlay,
                onPause = onVideoPlayerPause,
            )

            VideoPlayerControllerPositionCtrl(
                currentPositionProvider = videoPlayerCurrentPositionProvider,
                durationProvider = videoPlayerDurationProvider,
                seekToPositionProvider = { seekToPosition},
                seekForward = ::seekForward,
                seekNext = ::seekNext,
            )
        }
    }
}

@Stable
class Debounce internal constructor(
    @param:IntRange(from = 0) private val wait: Long,
    private val func: () -> Unit = {},
) {
    fun active() {
        channel.trySend(wait)
    }

    private val channel = Channel<Long>(Channel.CONFLATED)

    @OptIn(FlowPreview::class)
    suspend fun observe() {
        channel.consumeAsFlow().debounce { it }.collect {
            func()
        }
    }
}

@Composable
fun rememberDebounce(
    @IntRange(from = 0) wait: Long,
    func: () -> Unit = {},
) = remember { Debounce(wait = wait, func = func) }.also {
    LaunchedEffect(it) { it.observe() }
}

@Preview(device = "id:Android TV (720p)")
@Composable
private fun VideoPlayerControllerScreenPreview() {
    MyTvTheme {
        PreviewWithLayoutGrids {
            VideoPlayerControllerScreen(
                videoPlayerCurrentPositionProvider = { System.currentTimeMillis() },
                videoPlayerDurationProvider = {
                    System.currentTimeMillis() - 1000L * 60 * 60 to System.currentTimeMillis() + 1000L * 60 * 60
                },
            )
        }
    }
}