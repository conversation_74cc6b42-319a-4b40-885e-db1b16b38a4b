{"logs": [{"outputFile": "top.yogiczy.mytv.tv-mergeOriginalDebugResources-77:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\75b82da373a07aa04f7fa0a8c467d672\\transformed\\media3-exoplayer-1.8.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,192,261,331,407,483,581,676", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "128,187,256,326,402,478,576,671,753"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7895,7973,8032,8101,8171,8247,8323,8421,8516", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "7968,8027,8096,8166,8242,8318,8416,8511,8593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\71973fdd2f8b1122e619daae98b107aa\\transformed\\appcompat-1.7.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "935,1058,1163,1270,1353,1459,1585,1669,1748,1839,1932,2025,2120,2218,2311,2404,2498,2589,2680,2761,2872,2980,3078,3188,3293,3401,3561,21803", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "1053,1158,1265,1348,1454,1580,1664,1743,1834,1927,2020,2115,2213,2306,2399,2493,2584,2675,2756,2867,2975,3073,3183,3288,3396,3556,3655,21880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\cdd23b539dbc00d913771dd5e581033e\\transformed\\foundation-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,144,242", "endColumns": "88,97,101", "endOffsets": "139,237,339"}, "to": {"startLines": "50,262,263", "startColumns": "4,4,4", "startOffsets": "3660,22891,22989", "endColumns": "88,97,101", "endOffsets": "3744,22984,23086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\03c59f01b4d50573fe2a148684529e0c\\transformed\\material-1.12.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,523,626,718,819,947,1031,1092,1157,1254,1334,1399,1494,1558,1630,1692,1768,1831,1888,2009,2067,2128,2185,2265,2402,2489,2564,2657,2737,2821,2960,3038,3117,3269,3358,3434,3491,3547,3613,3691,3772,3843,3931,4009,4086,4160,4239,4349,4439,4531,4623,4724,4798,4880,4981,5031,5114,5180,5272,5359,5421,5485,5548,5621,5744,5857,5961,6069,6130,6190,6276,6362,6439", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,87,102,91,100,127,83,60,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,74,92,79,83,138,77,78,151,88,75,56,55,65,77,80,70,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85,85,76,78", "endOffsets": "269,350,430,518,621,713,814,942,1026,1087,1152,1249,1329,1394,1489,1553,1625,1687,1763,1826,1883,2004,2062,2123,2180,2260,2397,2484,2559,2652,2732,2816,2955,3033,3112,3264,3353,3429,3486,3542,3608,3686,3767,3838,3926,4004,4081,4155,4234,4344,4434,4526,4618,4719,4793,4875,4976,5026,5109,5175,5267,5354,5416,5480,5543,5616,5739,5852,5956,6064,6125,6185,6271,6357,6434,6513"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,71,72,123,124,127,185,186,187,188,189,190,191,192,193,194,195,196,197,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,246,250,251,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "761,3749,3830,3910,3998,4101,4924,5025,5153,5714,5775,9663,9760,10009,16483,16578,16642,16714,16776,16852,16915,16972,17093,17151,17212,17269,17349,17575,17662,17737,17830,17910,17994,18133,18211,18290,18442,18531,18607,18664,18720,18786,18864,18945,19016,19104,19182,19259,19333,19412,19522,19612,19704,19796,19897,19971,20053,20154,20204,20287,20353,20445,20532,20594,20658,20721,20794,20917,21030,21134,21242,21303,21540,21885,21971,22124", "endLines": "22,51,52,53,54,55,63,64,65,71,72,123,124,127,185,186,187,188,189,190,191,192,193,194,195,196,197,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,246,250,251,253", "endColumns": "12,80,79,87,102,91,100,127,83,60,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,74,92,79,83,138,77,78,151,88,75,56,55,65,77,80,70,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85,85,76,78", "endOffsets": "930,3825,3905,3993,4096,4188,5020,5148,5232,5770,5835,9755,9835,10069,16573,16637,16709,16771,16847,16910,16967,17088,17146,17207,17264,17344,17481,17657,17732,17825,17905,17989,18128,18206,18285,18437,18526,18602,18659,18715,18781,18859,18940,19011,19099,19177,19254,19328,19407,19517,19607,19699,19791,19892,19966,20048,20149,20199,20282,20348,20440,20527,20589,20653,20716,20789,20912,21025,21129,21237,21298,21358,21621,21966,22043,22198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\35f391f48f1163378f0ecf1dfb563ddd\\transformed\\material3-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,414,534,634,732,847,989,1104,1263,1347,1445,1543,1644,1761,1890,1993,2134,2274,2415,2581,2714,2831,2952,3081,3180,3277,3398,3543,3649,3762,3876,4015,4160,4269,4376,4462,4563,4664,4775,4861,4947,5058,5138,5222,5323,5431,5530,5634,5721,5834,5934,6041,6160,6240,6357", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,110,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "171,290,409,529,629,727,842,984,1099,1258,1342,1440,1538,1639,1756,1885,1988,2129,2269,2410,2576,2709,2826,2947,3076,3175,3272,3393,3538,3644,3757,3871,4010,4155,4264,4371,4457,4558,4659,4770,4856,4942,5053,5133,5217,5318,5426,5525,5629,5716,5829,5929,6036,6155,6235,6352,6459"}, "to": {"startLines": "128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10074,10195,10314,10433,10553,10653,10751,10866,11008,11123,11282,11366,11464,11562,11663,11780,11909,12012,12153,12293,12434,12600,12733,12850,12971,13100,13199,13296,13417,13562,13668,13781,13895,14034,14179,14288,14395,14481,14582,14683,14794,14880,14966,15077,15157,15241,15342,15450,15549,15653,15740,15853,15953,16060,16179,16259,16376", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,110,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "10190,10309,10428,10548,10648,10746,10861,11003,11118,11277,11361,11459,11557,11658,11775,11904,12007,12148,12288,12429,12595,12728,12845,12966,13095,13194,13291,13412,13557,13663,13776,13890,14029,14174,14283,14390,14476,14577,14678,14789,14875,14961,15072,15152,15236,15337,15445,15544,15648,15735,15848,15948,16055,16174,16254,16371,16478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\07b9e5f639314a52052890932176ce08\\transformed\\media3-ui-1.8.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,487,666,752,840,923,1022,1120,1201,1267,1380,1490,1563,1632,1698,1769,1879,1990,2099,2168,2256,2331,2413,2502,2593,2657,2721,2774,2832,2880,2941,3006,3075,3140,3212,3276,3333,3399,3452,3512,3586,3660,3717", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,87,82,98,97,80,65,112,109,72,68,65,70,109,110,108,68,87,74,81,88,90,63,63,52,57,47,60,64,68,64,71,63,56,65,52,59,73,73,56,68", "endOffsets": "280,482,661,747,835,918,1017,1115,1196,1262,1375,1485,1558,1627,1693,1764,1874,1985,2094,2163,2251,2326,2408,2497,2588,2652,2716,2769,2827,2875,2936,3001,3070,3135,3207,3271,3328,3394,3447,3507,3581,3655,3712,3781"}, "to": {"startLines": "2,11,15,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,582,5840,5926,6014,6097,6196,6294,6375,6441,6554,6664,6737,6806,6872,6943,7053,7164,7273,7342,7430,7505,7587,7676,7767,7831,8598,8651,8709,8757,8818,8883,8952,9017,9089,9153,9210,9276,9329,9389,9463,9537,9594", "endLines": "10,14,18,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122", "endColumns": "17,12,12,85,87,82,98,97,80,65,112,109,72,68,65,70,109,110,108,68,87,74,81,88,90,63,63,52,57,47,60,64,68,64,71,63,56,65,52,59,73,73,56,68", "endOffsets": "375,577,756,5921,6009,6092,6191,6289,6370,6436,6549,6659,6732,6801,6867,6938,7048,7159,7268,7337,7425,7500,7582,7671,7762,7826,7890,8646,8704,8752,8813,8878,8947,9012,9084,9148,9205,9271,9324,9384,9458,9532,9589,9658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\e4a290dba5f52ccc9bc02699d6e15cbe\\transformed\\material-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "198", "startColumns": "4", "startOffsets": "17486", "endColumns": "88", "endOffsets": "17570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\0c82a76523f617aef246a3faf4397c6a\\transformed\\ui-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "195,292,376,480,583,672,750,841,932,1018,1104,1195,1271,1356,1431,1510,1585,1667,1738", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,84,74,78,74,81,70,119", "endOffsets": "287,371,475,578,667,745,836,927,1013,1099,1190,1266,1351,1426,1505,1580,1662,1733,1853"}, "to": {"startLines": "66,67,68,69,70,125,126,244,245,247,248,252,254,255,256,257,259,260,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5237,5334,5418,5522,5625,9840,9918,21363,21454,21626,21712,22048,22203,22288,22363,22442,22618,22700,22771", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,84,74,78,74,81,70,119", "endOffsets": "5329,5413,5517,5620,5709,9913,10004,21449,21535,21707,21798,22119,22283,22358,22437,22512,22695,22766,22886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\c64c27f9b5a3bd14c8d70afc2c5cfe89\\transformed\\core-1.16.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "56,57,58,59,60,61,62,258", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4193,4289,4391,4490,4587,4693,4798,22517", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "4284,4386,4485,4582,4688,4793,4919,22613"}}]}]}