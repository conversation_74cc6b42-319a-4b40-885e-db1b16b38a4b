{"version": 3, "artifactType": {"type": "LINKED_RESOURCES_BINARY_FORMAT", "kind": "Directory"}, "applicationId": "com.github.mytv.android", "variantName": "originalDebug", "elements": [{"type": "UNIVERSAL", "filters": [], "attributes": [], "versionCode": 1, "versionName": "2.0.0.0", "outputFile": "linked-resources-binary-format-originalUniversalDebug.ap_"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "armeabi-v7a"}], "attributes": [], "versionCode": 1, "versionName": "2.0.0.0", "outputFile": "linked-resources-binary-format-originalArmeabi-v7aDebug.ap_"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "x86"}], "attributes": [], "versionCode": 1, "versionName": "2.0.0.0", "outputFile": "linked-resources-binary-format-originalX86Debug.ap_"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "arm64-v8a"}], "attributes": [], "versionCode": 1, "versionName": "2.0.0.0", "outputFile": "linked-resources-binary-format-originalArm64-v8aDebug.ap_"}, {"type": "ONE_OF_MANY", "filters": [{"filterType": "ABI", "value": "x86_64"}], "attributes": [], "versionCode": 1, "versionName": "2.0.0.0", "outputFile": "linked-resources-binary-format-originalX86_64Debug.ap_"}], "elementType": "File"}