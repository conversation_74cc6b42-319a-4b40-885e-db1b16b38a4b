<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":ijkplayer-java" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\ijkplayer-java\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config="wang.harlon.quickjs:wrapper-android:3.2.3" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\bd404f1a89b4bb36407c692e019915c8\transformed\wrapper-android-3.2.3\assets"><file name="test_assert_define.js" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\bd404f1a89b4bb36407c692e019915c8\transformed\wrapper-android-3.2.3\assets\test_assert_define.js"/><file name="test_base_module1.mjs" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\bd404f1a89b4bb36407c692e019915c8\transformed\wrapper-android-3.2.3\assets\test_base_module1.mjs"/><file name="test_base_module2.mjs" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\bd404f1a89b4bb36407c692e019915c8\transformed\wrapper-android-3.2.3\assets\test_base_module2.mjs"/><file name="test_module_import_dynamic.js" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\bd404f1a89b4bb36407c692e019915c8\transformed\wrapper-android-3.2.3\assets\test_module_import_dynamic.js"/><file name="test_polyfill_date.js" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\bd404f1a89b4bb36407c692e019915c8\transformed\wrapper-android-3.2.3\assets\test_polyfill_date.js"/></source></dataSet><dataSet config="com.squareup.okhttp3:okhttp-android:5.1.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\assets"><file name="PublicSuffixDatabase.list" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\332f99bd6086cb1e6dbe2da88fc0943c\transformed\okhttp-release\assets\PublicSuffixDatabase.list"/></source></dataSet><dataSet config="org.videolan.android:libvlc-all:3.6.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets"><file name="hrtfs/dodeca_and_7channel_3DSL_HRTF.sofa" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\hrtfs\dodeca_and_7channel_3DSL_HRTF.sofa"/><file name="lua/meta/art/00_musicbrainz.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\meta\art\00_musicbrainz.lua"/><file name="lua/meta/art/01_googleimage.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\meta\art\01_googleimage.lua"/><file name="lua/meta/art/02_frenchtv.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\meta\art\02_frenchtv.lua"/><file name="lua/meta/art/03_lastfm.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\meta\art\03_lastfm.lua"/><file name="lua/meta/reader/filename.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\meta\reader\filename.lua"/><file name="lua/modules/common.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\modules\common.lua"/><file name="lua/modules/dkjson.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\modules\dkjson.lua"/><file name="lua/modules/sandbox.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\modules\sandbox.lua"/><file name="lua/modules/simplexml.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\modules\simplexml.lua"/><file name="lua/playlist/anevia_streams.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\anevia_streams.lua"/><file name="lua/playlist/anevia_xml.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\anevia_xml.lua"/><file name="lua/playlist/appletrailers.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\appletrailers.lua"/><file name="lua/playlist/bbc_co_uk.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\bbc_co_uk.lua"/><file name="lua/playlist/break.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\break.lua"/><file name="lua/playlist/cue.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\cue.lua"/><file name="lua/playlist/dailymotion.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\dailymotion.lua"/><file name="lua/playlist/extreme.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\extreme.lua"/><file name="lua/playlist/france2.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\france2.lua"/><file name="lua/playlist/jamendo.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\jamendo.lua"/><file name="lua/playlist/katsomo.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\katsomo.lua"/><file name="lua/playlist/koreus.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\koreus.lua"/><file name="lua/playlist/lelombrik.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\lelombrik.lua"/><file name="lua/playlist/liveleak.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\liveleak.lua"/><file name="lua/playlist/metacafe.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\metacafe.lua"/><file name="lua/playlist/mpora.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\mpora.lua"/><file name="lua/playlist/newgrounds.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\newgrounds.lua"/><file name="lua/playlist/pinkbike.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\pinkbike.lua"/><file name="lua/playlist/rockbox_fm_presets.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\rockbox_fm_presets.lua"/><file name="lua/playlist/soundcloud.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\soundcloud.lua"/><file name="lua/playlist/twitch.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\twitch.lua"/><file name="lua/playlist/vimeo.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\vimeo.lua"/><file name="lua/playlist/vocaroo.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\vocaroo.lua"/><file name="lua/playlist/youtube.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\youtube.lua"/><file name="lua/playlist/zapiks.lua" path="C:\Users\<USER>\.gradle\caches\8.14.2\transforms\4f835832c7d17909dbbc4abc991695cc\transformed\libvlc-all-3.6.2\assets\lua\playlist\zapiks.lua"/></source></dataSet><dataSet config=":webview" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\webview\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":core:designsystem" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\core\designsystem\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":core:data" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\core\data\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":core:util" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\core\util\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\assets"><file name="remote-configs/assets/index-WrbONkk7.js" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\assets\remote-configs\assets\index-WrbONkk7.js"/><file name="remote-configs/assets/style-BwpPies-.css" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\assets\remote-configs\assets\style-BwpPies-.css"/><file name="remote-configs/index.html" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\assets\remote-configs\index.html"/><file name="remote-configs-en/assets/index-DHGrdmgM.js" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\assets\remote-configs-en\assets\index-DHGrdmgM.js"/><file name="remote-configs-en/assets/style-D7X8VXyM.css" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\assets\remote-configs-en\assets\style-D7X8VXyM.css"/><file name="remote-configs-en/index.html" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\assets\remote-configs-en\index.html"/><file name="webview_loading_blacklist.json" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\assets\webview_loading_blacklist.json"/><file name="webview_player_impl.js" path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\main\assets\webview_player_impl.js"/></source></dataSet><dataSet config="original" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\original\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\debug\assets"/></dataSet><dataSet config="variant" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\src\originalDebug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\mytv-Internal\tv\build\intermediates\shader_assets\originalDebug\compileOriginalDebugShaders\out"/></dataSet></merger>